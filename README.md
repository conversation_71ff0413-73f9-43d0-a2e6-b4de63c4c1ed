# AZO Mobile App

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
   npx expo start --tunnel
   ```

## APK

```bash
npx expo-doctor
npx expo install --check
npx expo export:embed --eager --platform android --dev false
```

```bash
eas login
eas build --platform android --profile preview
```

https://expo.dev/accounts/klehjgd07f/projects/hello-world-app/builds